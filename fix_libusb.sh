#!/bin/bash

echo "修复Digispark上传问题的脚本"
echo "================================"

# 检查libusb是否已安装
if ! brew list libusb-compat &> /dev/null; then
    echo "正在安装libusb-compat..."
    brew install libusb-compat
fi

# 创建符号链接目录
echo "创建符号链接..."
sudo mkdir -p /usr/local/opt/libusb-compat/lib

# 创建符号链接
sudo ln -sf /opt/homebrew/lib/libusb-0.1.4.dylib /usr/local/opt/libusb-compat/lib/libusb-0.1.4.dylib

echo "修复完成！现在可以尝试上传代码到Digispark了。"
echo ""
echo "使用方法："
echo "1. 在Arduino IDE中打开badusb_clean.ino"
echo "2. 选择板子：Tools -> Board -> Digistump AVR Boards -> Digispark (Default - 16.5MHz)"
echo "3. 点击上传按钮"
echo "4. 在提示时插入Digispark开发板"
