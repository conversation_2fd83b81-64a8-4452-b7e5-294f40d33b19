#!/bin/bash

echo "修复Apple Silicon Mac上的Digispark上传问题"
echo "=========================================="

# 检查是否为Apple Silicon Mac
if [[ $(uname -m) == "arm64" ]]; then
    echo "检测到Apple Silicon Mac，需要特殊处理..."

    # 检查是否已安装x86_64版本的Homebrew
    if [ ! -f "/usr/local/bin/brew" ]; then
        echo "安装x86_64版本的Homebrew..."
        arch -x86_64 /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi

    # 使用x86_64版本的brew安装libusb-compat
    echo "安装x86_64版本的libusb-compat..."
    arch -x86_64 /usr/local/bin/brew install libusb-compat

    # 创建符号链接
    echo "创建符号链接..."
    sudo mkdir -p /usr/local/opt/libusb-compat/lib
    sudo ln -sf /usr/local/lib/libusb-0.1.4.dylib /usr/local/opt/libusb-compat/lib/libusb-0.1.4.dylib

    echo ""
    echo "✅ 修复完成！"
    echo ""
    echo "现在请按以下步骤操作："
    echo "1. 关闭Arduino IDE"
    echo "2. 在应用程序中找到Arduino IDE"
    echo "3. 右键点击Arduino IDE -> 显示简介"
    echo "4. 勾选'使用Rosetta打开'"
    echo "5. 重新启动Arduino IDE"
    echo "6. 现在可以正常上传到Digispark了"

else
    echo "这是Intel Mac，使用标准方法..."
    brew install libusb-compat
    sudo mkdir -p /usr/local/opt/libusb-compat/lib
    sudo ln -sf /opt/homebrew/lib/libusb-0.1.4.dylib /usr/local/opt/libusb-compat/lib/libusb-0.1.4.dylib
    echo "✅ 修复完成！"
fi
