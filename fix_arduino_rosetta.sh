#!/bin/bash

echo "为Arduino IDE添加Rosetta支持"
echo "============================"

# 检查Arduino IDE是否存在
if [ ! -d "/Applications/Arduino IDE.app" ]; then
    echo "❌ 未找到Arduino IDE，请先安装Arduino IDE"
    exit 1
fi

# 检查架构
ARCH=$(file "/Applications/Arduino IDE.app/Contents/MacOS/Arduino IDE" | grep -o "arm64\|x86_64")

if [ "$ARCH" = "arm64" ]; then
    echo "检测到ARM64版本的Arduino IDE"
    echo "正在修改Info.plist以支持Rosetta..."
    
    # 备份原始Info.plist
    sudo cp "/Applications/Arduino IDE.app/Contents/Info.plist" "/Applications/Arduino IDE.app/Contents/Info.plist.backup"
    
    # 添加LSRequiresIPhoneOS键以强制显示Rosetta选项
    sudo /usr/libexec/PlistBuddy -c "Add :LSRequiresIPhoneOS bool false" "/Applications/Arduino IDE.app/Contents/Info.plist" 2>/dev/null || true
    
    echo "✅ 修改完成！"
    echo ""
    echo "现在请按以下步骤操作："
    echo "1. 重启Finder（按住Option键，右键Dock中的Finder，选择重新启动）"
    echo "2. 在应用程序中找到Arduino IDE"
    echo "3. 右键点击Arduino IDE → 显示简介"
    echo "4. 现在应该能看到'使用Rosetta打开'选项了"
    echo "5. 勾选该选项"
    echo "6. 重新启动Arduino IDE"
    
elif [ "$ARCH" = "x86_64" ]; then
    echo "✅ 检测到Intel版本的Arduino IDE，无需修改"
else
    echo "❌ 无法确定Arduino IDE架构"
    exit 1
fi
