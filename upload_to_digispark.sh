#!/bin/bash

echo "Digispark命令行上传工具"
echo "======================"

# 检查参数
if [ $# -eq 0 ]; then
    echo "使用方法: $0 <sketch.ino文件>"
    echo "例如: $0 badusb_final.ino"
    exit 1
fi

SKETCH_FILE="$1"

# 检查文件是否存在
if [ ! -f "$SKETCH_FILE" ]; then
    echo "❌ 文件不存在: $SKETCH_FILE"
    exit 1
fi

echo "正在编译 $SKETCH_FILE..."

# 创建临时目录
TEMP_DIR="/tmp/digispark_compile"
mkdir -p "$TEMP_DIR"

# 复制sketch文件到临时目录
SKETCH_NAME=$(basename "$SKETCH_FILE" .ino)
SKETCH_DIR="$TEMP_DIR/$SKETCH_NAME"
mkdir -p "$SKETCH_DIR"
cp "$SKETCH_FILE" "$SKETCH_DIR/$SKETCH_NAME.ino"

# 使用arduino-cli编译（如果安装了）
if command -v arduino-cli &> /dev/null; then
    echo "使用arduino-cli编译..."
    arduino-cli compile --fqbn digistump:avr:digispark-tiny "$SKETCH_DIR"
    
    if [ $? -eq 0 ]; then
        echo "✅ 编译成功！"
        echo "现在请插入Digispark开发板..."
        
        # 使用micronucleus上传
        HEX_FILE="$SKETCH_DIR/build/digistump.avr.digispark-tiny/$SKETCH_NAME.ino.hex"
        if [ -f "$HEX_FILE" ]; then
            echo "正在上传..."
            arch -x86_64 /Users/<USER>/Library/Arduino15/packages/digistump/tools/micronucleus/2.6/micronucleus --run "$HEX_FILE"
        else
            echo "❌ 未找到编译后的hex文件"
        fi
    else
        echo "❌ 编译失败"
    fi
else
    echo "❌ 未安装arduino-cli"
    echo "请先安装: brew install arduino-cli"
fi

# 清理临时文件
rm -rf "$TEMP_DIR"
