# 虚拟机测试指南

## 🚨 重要提醒

在Mac上测试BadUSB时，某些快捷键会影响宿主机（Mac）：

### ❌ 避免的快捷键：
- **Win + Space** - 会唤醒Mac的Spotlight搜索
- **Win + Tab** - 可能影响Mac的Mission Control
- **Cmd相关快捷键** - 可能被Mac拦截

### ✅ 安全的快捷键：
- **Ctrl + Shift** - 输入法切换，不影响Mac
- **Shift + Alt** - 输入法切换，不影响Mac
- **Win + R** - 只影响Windows虚拟机

## 📁 推荐的虚拟机测试文件

### 🔥 最佳选择：`badusb_vm_safe.ino`
- 专门为虚拟机测试优化
- 移除了所有可能唤醒Mac的快捷键
- 只使用Ctrl+Shift切换输入法

### 🔧 备选方案：`badusb_ascii_input.ino`
- 已修复Win+Space问题
- 使用ASCII码输入，绕过输入法

## 🛠️ 虚拟机设置建议

### VMware Fusion / Parallels Desktop：
1. **启用"隔离Mac快捷键"**
2. **设置USB设备直通**
3. **禁用"共享Mac快捷键"**

### VirtualBox：
1. **设置 -> 输入 -> 取消勾选"自动捕获键盘"**
2. **USB设置 -> 启用USB 2.0控制器**

## 🎯 测试步骤

1. **启动Windows虚拟机**
2. **确保虚拟机获得焦点**（点击虚拟机窗口）
3. **插入编程好的Digispark**
4. **观察行为**

## 🔍 故障排除

### 如果还是唤醒Mac：
1. 检查虚拟机软件的快捷键设置
2. 确保虚拟机处于全屏或专注模式
3. 使用 `badusb_vm_safe.ino` 版本

### 如果虚拟机中不工作：
1. 检查USB设备是否正确连接到虚拟机
2. 确认Windows虚拟机识别了Digispark
3. 检查虚拟机的USB设置

## 💡 最佳实践

1. **测试前先备份重要数据**
2. **使用快照功能**，方便恢复
3. **先在虚拟机中测试无害命令**
4. **确认网络连接正常**
