# Apple Silicon Mac Digispark 修复指南

## 🚨 问题说明
你的Mac是Apple Silicon (M1/M2/M3)，但Digispark的micronucleus工具需要x86_64架构的libusb库。

## 🔧 解决方案

### 方法1：使用Rosetta运行Arduino IDE（推荐）

1. **关闭Arduino IDE**

2. **设置Arduino IDE使用Rosetta**：
   - 在应用程序文件夹中找到Arduino IDE
   - 右键点击Arduino IDE → "显示简介"
   - 勾选"使用Rosetta打开"
   - 关闭信息窗口

3. **安装x86_64版本的libusb**：
   ```bash
   ./fix_digispark_m1.sh
   ```

4. **重新启动Arduino IDE**
   - 现在Arduino IDE会以x86_64模式运行
   - 可以正常上传到Digispark

### 方法2：手动安装x86_64 Homebrew和libusb

```bash
# 安装x86_64版本的Homebrew（如果没有）
arch -x86_64 /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装x86_64版本的libusb-compat
arch -x86_64 /usr/local/bin/brew install libusb-compat

# 创建符号链接
sudo mkdir -p /usr/local/opt/libusb-compat/lib
sudo ln -sf /usr/local/lib/libusb-0.1.4.dylib /usr/local/opt/libusb-compat/lib/libusb-0.1.4.dylib
```

## ✅ 验证修复

1. 重新启动Arduino IDE（确保使用Rosetta）
2. 打开 `badusb_final.ino`
3. 选择板子：Tools → Board → Digistump AVR Boards → Digispark (Default - 16.5MHz)
4. 点击上传按钮
5. 在提示时插入Digispark开发板

## 🎯 注意事项

- Arduino IDE必须使用Rosetta运行
- 编译警告是正常的，不影响功能
- 如果还有问题，重启Mac后再试

## 🔍 故障排除

如果仍然有问题：
1. 确认Arduino IDE确实在使用Rosetta（活动监视器中显示为"Intel"）
2. 检查libusb库是否正确安装：`ls -la /usr/local/lib/libusb-0.1.4.dylib`
3. 重启Arduino IDE
