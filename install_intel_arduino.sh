#!/bin/bash

echo "下载Intel版本的Arduino IDE用于Digispark开发"
echo "============================================"

# 创建临时目录
TEMP_DIR="/tmp/arduino_intel"
mkdir -p "$TEMP_DIR"
cd "$TEMP_DIR"

echo "正在下载Intel版本的Arduino IDE..."
# 下载Arduino IDE 1.8.19 (Intel版本，兼容性更好)
curl -L -o arduino-1.8.19-macosx.zip "https://downloads.arduino.cc/arduino-1.8.19-macosx.zip"

if [ $? -eq 0 ]; then
    echo "下载完成，正在解压..."
    unzip -q arduino-1.8.19-macosx.zip
    
    # 检查是否已存在Arduino IDE
    if [ -d "/Applications/Arduino IDE.app" ]; then
        echo "检测到现有的Arduino IDE，将重命名为Arduino IDE (ARM).app"
        mv "/Applications/Arduino IDE.app" "/Applications/Arduino IDE (ARM).app"
    fi
    
    # 移动Intel版本到应用程序文件夹
    echo "安装Intel版本的Arduino IDE..."
    mv Arduino.app "/Applications/Arduino IDE (Intel).app"
    
    echo ""
    echo "✅ 安装完成！"
    echo ""
    echo "现在你有两个版本的Arduino IDE："
    echo "- Arduino IDE (ARM).app - 原生ARM版本"
    echo "- Arduino IDE (Intel).app - Intel版本（用于Digispark）"
    echo ""
    echo "使用Intel版本进行Digispark开发："
    echo "1. 打开 Arduino IDE (Intel).app"
    echo "2. 安装Digispark库（如果还没安装）"
    echo "3. 现在可以正常上传到Digispark了"
    
    # 清理临时文件
    cd /
    rm -rf "$TEMP_DIR"
else
    echo "❌ 下载失败，请检查网络连接"
    exit 1
fi
